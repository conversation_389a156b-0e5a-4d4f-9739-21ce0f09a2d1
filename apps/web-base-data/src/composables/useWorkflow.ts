import type { Ref } from 'vue';

import { computed, nextTick, ref } from 'vue';

import { getApprovalDetailApi, getProcessDefinitionApi } from '#/api';

export interface WorkflowConfig {
  key?: string;
  processInstanceId?: string;
}
export interface workflowOptions {
  operationButtonRef: Ref<any>;
}

export function useWorkflow(options?: workflowOptions) {
  const config = ref<WorkflowConfig>({});
  const processDefinition = ref<any>({});
  const approvalDetail = ref<any>({});
  const processInstance = ref<any>({});
  const isWorkflow = computed(() => config.value.key && config.value.processInstanceId);
  const getProcessDefinitionDetail = async () => {
    if (!config.value.key) return;
    try {
      processDefinition.value = await getProcessDefinitionApi({ key: config.value.key });
    } catch (error) {
      console.error('获取流程定义失败', error);
    }
  };
  const getApprovalDetail = async () => {
    if (!config.value.processInstanceId) return;
    approvalDetail.value = await getApprovalDetailApi({
      processInstanceId: config.value.processInstanceId,
    });
    processInstance.value = approvalDetail.value.processInstance;
  };
  const initWorkflow = async (data?: WorkflowConfig) => {
    config.value = data ?? {};
    await getProcessDefinitionDetail();
    await getApprovalDetail();
    if (options?.operationButtonRef) {
      await nextTick();
      options.operationButtonRef.value?.loadTodoTask(approvalDetail.value.todoTask);
    }
  };
  return {
    initWorkflow,
    isWorkflow,
    processDefinition,
    processInstance,
  };
}
