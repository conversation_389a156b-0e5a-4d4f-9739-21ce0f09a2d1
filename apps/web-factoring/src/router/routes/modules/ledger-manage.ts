import type { RouteRecordRaw } from 'vue-router';

import { $t } from '@vben/locales';

import { BasicLayout } from '#/layouts';

const routes: RouteRecordRaw[] = [
  {
    component: BasicLayout,
    meta: {
      icon: '',
      title: $t('page.ledgerManage.title'),
    },
    name: 'LedgerManage',
    path: '/ledger-manage',
    children: [
      {
        name: 'LedgerManageDistrictQuota',
        path: '/ledger-manage/district-quota',
        component: () => import('#/views/ledger-manage/district-quota/index.vue'),
        meta: {
          icon: '',
          title: $t('page.ledgerManage.districtQuota'),
        },
      },
      {
        name: 'LedgerManageCompanyQuota',
        path: '/ledger-manage/company-quota',
        component: () => import('#/views/ledger-manage/company-quota/index.vue'),
        meta: {
          icon: '',
          title: $t('page.ledgerManage.companyQuota'),
        },
      },
      {
        name: 'LedgerManageFinance',
        path: '/ledger-manage/finance',
        component: () => import('#/views/ledger-manage/finance/index.vue'),
        meta: {
          icon: '',
          title: $t('page.ledgerManage.finance'),
        },
      },
    ],
  },
];

export default routes;
