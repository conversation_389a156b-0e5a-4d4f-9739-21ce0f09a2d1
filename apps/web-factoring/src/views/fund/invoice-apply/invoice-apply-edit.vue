<script setup lang="ts">
import type { InvoiceApplyInfo, InvoiceRefBO } from '#/api';

import { reactive, ref } from 'vue';

import { BASE_PAGE_CLASS_NAME, COL_SPAN_PROP, FORM_PROP, FULL_FORM_ITEM_PROP } from '@vben/constants';
import { BasicPopup, usePopupInner } from '@vben/fe-ui';
import { useDictStore } from '@vben/stores';

import { message } from 'ant-design-vue';
import BigNumber from 'bignumber.js';
import dayjs from 'dayjs';
import { cloneDeep, isEmpty } from 'lodash-es';

import { BaseAttachmentList } from '#/adapter/base-ui';
import {
  addInvoiceApplyApi,
  editInvoiceApplyApi,
  getCompanyInvoiceListApi,
  getCompanyListApi,
  getInvoiceApplyInfoApi,
  getInvoiceDetailApi,
  getPaymentConfirmListApi,
  getProjectList<PERSON>pi,
  getRepaymentConfirmListApi,
} from '#/api';
import InvoiceContent from '#/views/fund/components/invoice-content.vue';

const emit = defineEmits(['ok', 'register']);

const dictStore = useDictStore();
const init = async (data: InvoiceApplyInfo) => {
  invoiceForm.value = {
    invoiceTax: 6,
  };
  if (data.id) {
    const info = data.id ? await getInvoiceApplyInfoApi(data.id as number) : data;
    info.applicationDate = info.applicationDate ? dayjs(info.applicationDate).valueOf().toString() : '';
    if (info.refList && info.invoiceItem) {
      const associationIds: number[] = [];
      if (info.invoiceItem === 'factoring_interest') {
        // 还款场景：提取 repaymentConfirmId
        info.refList.forEach((item: InvoiceRefBO) => {
          if (item.repaymentConfirmId) {
            associationIds.push(item.repaymentConfirmId);
          }
        });
      } else if (info.invoiceItem === 'service_fee') {
        // 服务费场景：提取 paymentConfirmId
        info.refList.forEach((item: InvoiceRefBO) => {
          if (item.paymentConfirmId) {
            associationIds.push(item.paymentConfirmId);
          }
        });
      }
      info.associationId = associationIds; // 赋值给 associationId
    }

    invoiceForm.value = { ...invoiceForm.value, ...info };
    if (invoiceForm.value.projectId && invoiceForm.value.invoiceItem) {
      await getResList();
    }
  }
};
const projectOptions = ref<any[]>([]);
const resOptions = ref<any[]>([]);
const getProjectList = async () => {
  const res = await getProjectListApi({ status: 'EFFECTIVE', isMeetingCompleted: 1 });
  projectOptions.value = res;
};
getProjectList();
const selectProject = () => {
  const project = projectOptions.value.find((item: object) => item.id === invoiceForm.value.projectId);
  invoiceForm.value.projectName = project?.projectName;
  if (!isEmpty(resOptions)) {
    resOptions.value = [];
    invoiceForm.value.associationId = [];
  }
  if (invoiceForm.value.projectId && invoiceForm.value.invoiceItem) {
    getResList();
  }
  getInvoiceDetail();
};
const getResList = async () => {
  let api;
  if (invoiceForm.value.invoiceItem === 'factoring_interest') api = getRepaymentConfirmListApi;
  if (invoiceForm.value.invoiceItem === 'service_fee') api = getPaymentConfirmListApi;
  const res = await api({
    projectId: invoiceForm.value.projectId,
    isInvoiceFlag: 1,
    isInvoiceId: invoiceForm.value.id,
  });
  if (!isEmpty(res)) {
    resOptions.value = res.map((item) => {
      const code =
        invoiceForm.value.invoiceItem === 'factoring_interest' ? item.repaymentConfirmCode : item.confirmCode;
      return {
        ...item,
        label: code,
        value: item.id,
      };
    });
  }
};
const selectAssociation = () => {
  const associationIds = invoiceForm.value.associationId;
  if (!associationIds || associationIds.length === 0) {
    invoiceForm.value.refList = []; // 清空关联列表
    return;
  }

  const refList: InvoiceRefBO[] = [];
  associationIds.forEach((id) => {
    // 从 resOptions 中匹配选中的记录
    const targetItem = resOptions.value.find((item) => item.value === id);
    if (!targetItem) return;

    if (invoiceForm.value.invoiceItem === 'factoring_interest') {
      // 还款记录：封装 repaymentConfirmId + repaymentConfirmCode
      refList.push({
        repaymentConfirmId: id,
        repaymentConfirmCode: targetItem.repaymentConfirmCode,
      });
    } else if (invoiceForm.value.invoiceItem === 'service_fee') {
      // 付款记录：封装 paymentConfirmId + paymentConfirmCode
      refList.push({
        paymentConfirmId: id,
        paymentConfirmCode: targetItem.confirmCode,
      });
    }
  });

  invoiceForm.value.refList = refList;
  getInvoiceDetail();
};
const getInvoiceDetail = async () => {
  if (isEmpty(invoiceForm.value.refList) || !invoiceForm.value.projectId || !invoiceForm.value.invoiceItem) return;
  const data = cloneDeep(invoiceForm.value);
  const detailList = await getInvoiceDetailApi(data);
  invoiceForm.value = { ...invoiceForm.value, detailList };
  // 获取详情后计算一次税额
  if (invoiceForm.value.invoiceTax) {
    invoiceTaxCalculation();
  }
};
// 计算税额和不含税金额的方法
const invoiceTaxCalculation = () => {
  // 检查必要数据是否存在
  if (!invoiceForm.value.invoiceTax || isEmpty(invoiceForm.value.detailList)) {
    return;
  }

  // 使用BigNumber处理税率，避免精度问题
  const taxRate = new BigNumber(invoiceForm.value.invoiceTax).dividedBy(100); // 转换为小数
  const divisor = taxRate.plus(1); // 计算除数 (税率/100 + 1)

  // 遍历明细列表进行计算
  invoiceForm.value.detailList = invoiceForm.value.detailList.map((item) => {
    if (item.amountTax === undefined || item.amountTax === null) {
      return item;
    }

    // 使用BigNumber进行精确计算
    const amountTax = new BigNumber(item.amountTax);

    // 计算不含税金额 = 价税合计 / (税率/100 + 1)
    const amount = amountTax.dividedBy(divisor).decimalPlaces(2, BigNumber.ROUND_HALF_UP);

    // 计算税额 = 价税合计 - 不含税金额
    const tax = amountTax.minus(amount).decimalPlaces(2, BigNumber.ROUND_HALF_UP);

    return {
      ...item,
      amount,
      tax,
    };
  });
};
const [registerPopup, { closePopup }] = usePopupInner(init);
const FormRef = ref();
const invoiceForm = ref<InvoiceApplyInfo>({});
const payeeOptions = ref<any[]>([]);
const getCompanyList = async () => {
  payeeOptions.value = await getCompanyListApi();
};
getCompanyList();
const selectCompany = async () => {
  invoiceForm.value.buyerName = payeeOptions.value.find(
    (item: object) => item.companyCode === invoiceForm.value.buyerCode,
  )?.companyName;
  const res = await getCompanyInvoiceListApi({ code: invoiceForm.value.buyerCode });
  const company = res.find((item: object) => item.isDefault === 1);
  invoiceForm.value.taxNumber = company?.taxNumber;
};
const loading = reactive({
  submit: false,
});
const save = async (type: string) => {
  await FormRef.value.validate();
  let api = addInvoiceApplyApi;
  if (invoiceForm.value.id) {
    api = editInvoiceApplyApi;
  }
  const formData = cloneDeep(invoiceForm.value);
  formData.applicationDate = Number(formData.applicationDate);
  loading.submit = true;
  if (type === 'submit') formData.isSubmit = true;
  try {
    const res = await api(formData as InvoiceApplyInfo);
    message.success('保存成功');
    closePopup();
    emit('ok', res);
  } finally {
    loading.submit = false;
  }
};
const rules = {
  projectId: [{ required: true, message: '请选择项目名称', trigger: 'change' }],
  associationId: [{ required: true, message: '请选择待开票关联项', trigger: 'change' }],
  invoiceItem: [{ required: true, message: '请选择开票项', trigger: 'change' }],
  invoiceTax: [{ required: true, message: '请输入开票税率' }],
  repaymentConfirmIds: [{ required: true, message: '请选择待开票还款记录', trigger: 'change' }],
  invoiceType: [{ required: true, message: '请选择发票类型', trigger: 'change' }],
  buyerCode: [{ required: true, message: '请输入购买方名称' }],
  taxNumber: [{ required: true, message: '请输入税号' }],
};
const formProp = { ...FORM_PROP, labelCol: { span: 6 }, wrapperCol: { span: 18 } };
const colSpan = COL_SPAN_PROP;
const fullProp = { ...FULL_FORM_ITEM_PROP, labelCol: { span: 3 }, wrapperCol: { span: 21 } };
</script>

<template>
  <BasicPopup v-bind="$attrs" title="开票申请" @register="registerPopup">
    <template #insertToolbar>
      <a-space>
        <a-button type="primary" :loading="loading.submit" @click="save('save')">保存</a-button>
        <a-button type="primary" :loading="loading.submit" @click="save('submit')">提交</a-button>
      </a-space>
    </template>
    <div :class="BASE_PAGE_CLASS_NAME">
      <a-form ref="FormRef" class="mt-5" :model="invoiceForm" :rules="rules" v-bind="formProp">
        <a-row class="mt-5">
          <a-col v-bind="colSpan">
            <a-form-item label="项目名称" name="projectId">
              <a-select
                v-model:value="invoiceForm.projectId"
                :options="projectOptions"
                :field-names="{ label: 'projectName', value: 'id' }"
                @change="selectProject"
              />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="开票项" name="invoiceItem">
              <a-select
                v-model:value="invoiceForm.invoiceItem"
                :options="dictStore.getDictList('FCT_INVOICE_ITME')"
                @change="selectProject"
              />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="开票税率(%)" name="invoiceTax">
              <a-input-number
                v-model:value="invoiceForm.invoiceTax"
                :controls="false"
                class="w-full"
                :precision="2"
                @blur="invoiceTaxCalculation"
              />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan" v-if="invoiceForm.invoiceItem">
            <a-form-item
              :label="`待开票${invoiceForm.invoiceItem === 'service_fee' ? '付款记录编号' : '还款记录编号'}`"
              name="associationId"
            >
              <a-select
                v-model:value="invoiceForm.associationId"
                :options="resOptions"
                @change="selectAssociation"
                mode="multiple"
              />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="发票类型" name="invoiceType">
              <a-select v-model:value="invoiceForm.invoiceType" :options="dictStore.getDictList('FCT_INVOICE_TYPE')" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="购买方名称" name="buyerCode">
              <a-select
                v-model:value="invoiceForm.buyerCode"
                :options="payeeOptions"
                :field-names="{ label: 'companyName', value: 'companyCode' }"
                @change="selectCompany"
              />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="税号" name="taxNumber">
              <a-input v-model:value="invoiceForm.taxNumber" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="开票申请日期" name="applicationDate">
              <a-date-picker v-model:value="invoiceForm.applicationDate" value-format="x" class="w-full" />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="备注" name="remarks" v-bind="fullProp">
              <a-textarea v-model:value="invoiceForm.remarks" :rows="4" />
            </a-form-item>
          </a-col>
        </a-row>
        <InvoiceContent :invoice-form="invoiceForm" />
        <BaseAttachmentList
          v-model="invoiceForm.attachmentList"
          :business-id="invoiceForm.id"
          business-type="FCT_INVOICE_OPEN"
          edit-mode
        />
      </a-form>
    </div>
  </BasicPopup>
</template>

<style></style>
