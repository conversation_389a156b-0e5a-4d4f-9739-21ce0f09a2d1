<script setup lang="ts">
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { RepaymentPlanInfo } from '#/api';

import { Page } from '@vben/common-ui';
import { usePopup } from '@vben/fe-ui';
import { $t } from '@vben/locales';
import { defineFormOptions } from '@vben/utils';

import { message } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { getRepaymentPlanCompleteApi, getRepaymentPlanPageListApi } from '#/api';

import RepaymentPlanDetail from './repayment-plan-detail.vue';

const formOptions = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'repaymentPlanCode',
      label: '还款计划编号',
    },
    {
      component: 'Input',
      fieldName: 'projectName',
      label: '项目名称',
    },
    {
      component: 'RangePicker',
      fieldName: 'launchDate',
      label: '投放日期',
    },
    {
      component: 'RangePicker',
      fieldName: 'dueDate',
      label: '最后还款日',
    },
  ],
  fieldMappingTime: [
    ['launchDate', ['beginLaunchDate', 'endLaunchDate'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']],
    ['dueDate', ['beginDueDate', 'endDueDate'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']],
  ],
  commonConfig: {
    labelCol: { span: 6 },
    wrapperCol: { span: 18 },
  },
});
const gridOptions: VxeTableGridOptions = {
  columns: [
    { field: 'repaymentPlanCode', title: '还款计划编号', minWidth: 180 },
    {
      field: 'projectName',
      title: '项目名称',
      minWidth: 200,
    },
    {
      field: 'projectType',
      title: '项目类型',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'FCT_PROJECT_TYPE',
        },
      },
    },
    {
      field: 'creditApplyName',
      title: '用信申请名称',
      minWidth: 200,
    },
    {
      field: 'paymentApplyCode',
      title: '关联付款申请',
      minWidth: 200,
    },
    {
      field: 'paymentConfirmCode',
      title: '关联付款记录',
      minWidth: 200,
    },
    {
      field: 'launchDate',
      title: '投放日期',
      minWidth: 120,
    },
    {
      field: 'dueDate',
      title: '最后还款日',
      minWidth: 120,
    },
    {
      field: 'status',
      title: '还款计划状态',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'FCT_REPAYMENT_PLAN_STATUS',
        },
      },
    },
    {
      field: 'repaymentStatus',
      title: '还款状态',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'FCT_REPAYMENT_STATUS',
        },
      },
    },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      width: 140,
      slots: { default: 'action' },
    },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await getRepaymentPlanPageListApi({
          current: page.currentPage,
          size: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  toolbarConfig: {
    slots: {
      tools: 'toolbar-tools',
    },
  },
};
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});
const [registerDetail, { openPopup: openDetailPopup }] = usePopup();
const viewDetail = (row: RepaymentPlanInfo) => {
  openDetailPopup(true, row);
};
const complete = async (row: RepaymentPlanInfo) => {
  await getRepaymentPlanCompleteApi(row.id as number);
  message.success($t('base.resSuccess'));
  await gridApi.reload();
};
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #action="{ row }">
        <a-space>
          <a-typography-link @click="viewDetail(row)">
            {{ $t('base.detail') }}
          </a-typography-link>
          <a-typography-link
            v-if="['activeS'].includes(row.status) && ['draft'].includes(row.repaymentStatus)"
            @click="complete(row)"
          >
            已完成还款
          </a-typography-link>
        </a-space>
      </template>
    </Grid>
    <RepaymentPlanDetail @register="registerDetail" />
  </Page>
</template>

<style></style>
