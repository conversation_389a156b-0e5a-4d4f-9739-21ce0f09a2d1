<script setup lang="ts">
import type { VxeTableGridOptions } from '#/adapter/vxe-table';

import { useVbenModal } from '@vben/common-ui';
import { defineFormOptions } from '@vben/utils';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { getCollateralPageListApi } from '#/api';

const formOptions = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'mortgageName',
      label: '抵押物名称',
    },
    {
      component: 'Input',
      fieldName: 'mortgageeCompanyName',
      label: '抵押权人',
    },
    {
      component: 'Input',
      fieldName: 'mortgageCompanyName',
      label: '抵押人',
    },
  ],
  showCollapseButton: false,
  submitOnEnter: true,
});
const gridOptions: VxeTableGridOptions = {
  columns: [
    { type: 'checkbox', width: '60px' },
    { field: 'mortgageName', title: '抵押物名称', width: 160 },
    { field: 'mortgageeCompanyName', title: '抵押权人' },
    { field: 'mortgageCompanyName', title: '抵押人' },
    { field: 'rightsCertNo', title: '他项权证编号' },
    { field: 'assessedAmount', title: '抵押物评估价值（元）' },
    { field: 'mortgageType', title: '抵押方式', formatter: ['formatStatus', 'FCT_MORTGAGE_TYPE'] },
    { field: 'mortgageAmount', title: '抵押价值/最高债权数额（元）' },
    { field: 'mortgageTerm', title: '抵押期限' },
    { field: 'propertyLocation', title: '不动产坐落' },
    { field: 'issuingDate', title: '出证日期', formatter: 'formatDate' },
  ],
  height: 500,
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await getCollateralPageListApi({
          ...formValues,
          statusList: 'EFFECTIVE',
          current: page.currentPage,
          size: page.pageSize,
        });
      },
    },
  },
  toolbarConfig: {
    custom: false,
    refresh: false,
    resizable: false,
    zoom: false,
  },
};
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});
const [Modal, modalApi] = useVbenModal({
  onOpened: () => {},
  onConfirm: async () => {
    const selectedRows = gridApi.grid.getCheckboxRecords();
    const processedRows = selectedRows.map(({ id: _id, ...rest }) => rest);
    modalApi.setData(processedRows);
    await modalApi.close();
  },
});
</script>

<template>
  <Modal title="选择抵押物" class="w-[80vw]">
    <Grid />
  </Modal>
</template>

<style></style>
