<script setup lang="ts">
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import { reactive, watch } from 'vue';

import { DETAIL_GRID_OPTIONS } from '@vben/constants';
import BasicCaption from '@vben/fe-ui/components/Basic/src/BasicCaption.vue';
import { useVbenVxeGrid } from '@vben/plugins/vxe-table';
import { useDictStore } from '@vben/stores';
import { formatDate } from '@vben/utils';

import { message } from 'ant-design-vue';
import BigNumber from 'bignumber.js';
import { isEmpty } from 'lodash-es';

import {
  calculationCreditApplyApi,
  calculationCreditPricingApi,
  calculationPaymentConfirmApi,
  calculationPricingApi,
} from '#/api';

const props = defineProps({
  calculationForm: { type: Object, default: () => ({}) },
  descriptionsProp: { type: Object, default: () => ({}) },
  calculationType: { type: String, default: '' },
});
const dictStore = useDictStore();

const calculationGridOptions = {
  columns: [
    {
      field: 'repayPeriods',
      title: '还款期数',
      minWidth: '100px',
    },
    {
      field: 'repaymentItem',
      title: '还款项',
      minWidth: '120px',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'FCT_REPAYMENT_ITEM',
        },
      },
    },
    {
      field: 'currentOperationDate',
      title: '当期还本/付息日',
      minWidth: '180px',
      formatter: 'formatDate',
    },
    {
      field: 'totalAmount',
      title: '当期净现金流(元)',
      minWidth: '180px',
    },
    {
      field: 'principalAmount',
      title: '应还本金(元)',
      minWidth: '180px',
    },
    {
      field: 'interestAmount',
      title: '应还利息(元)',
      minWidth: '180px',
    },
    {
      field: 'serviceAmount',
      title: '应收服务费(元)',
      minWidth: '180px',
    },
    {
      field: 'graceInterestAmount',
      title: '应还宽限期利息(元)',
      minWidth: '180px',
    },
    {
      field: 'overdueInterestAmount',
      title: '应还逾期罚息(元)',
      minWidth: '180px',
    },
  ],
  footerMethod({ $grid }) {
    const data = $grid?.getTableData().visibleData || [];
    let interestAmount = new BigNumber(0);
    let serviceAmount = new BigNumber(0);
    let totalAmount = new BigNumber(0);
    let principalAmount = new BigNumber(0);
    data.forEach((item) => {
      interestAmount = interestAmount.plus(new BigNumber(item.interestAmount || 0));
      serviceAmount = serviceAmount.plus(new BigNumber(item.serviceAmount || 0));
      totalAmount = totalAmount.plus(new BigNumber(item.totalAmount || 0));
      principalAmount = principalAmount.plus(new BigNumber(item.principalAmount || 0));
    });
    const footerRow = {
      checkbox: '合计',
      interestAmount: interestAmount.decimalPlaces(2, BigNumber.ROUND_HALF_UP).toNumber(),
      serviceAmount: serviceAmount.decimalPlaces(2, BigNumber.ROUND_HALF_UP).toNumber(),
      totalAmount: totalAmount.decimalPlaces(2, BigNumber.ROUND_HALF_UP).toNumber(),
      principalAmount: principalAmount.decimalPlaces(2, BigNumber.ROUND_HALF_UP).toNumber(),
    };
    return [footerRow];
  },
  ...DETAIL_GRID_OPTIONS,
  showFooter: true,
} as VxeTableGridOptions;
const [CalculationGrid, CalculationGridApi] = useVbenVxeGrid({
  gridOptions: calculationGridOptions,
});

const init = (data: any) => {
  CalculationGridApi.grid.reloadData(data.detailList ?? []);
};

watch(
  () => props.calculationForm,
  (val = {}) => {
    CalculationGridApi.grid.reloadData(val.detailList ?? []);
  },
  { deep: true },
);
const loading = reactive({
  download: false,
});
const exportCalculation = async () => {
  if (isEmpty(props.calculationForm.detailList)) {
    message.warning('没有试算明细信息，无法导出');
    return;
  }
  loading.download = true;
  const api = {
    Pricing: calculationPricingApi,
    CreditPricing: calculationCreditPricingApi,
    CreditApply: calculationCreditApplyApi,
    PaymentRecord: calculationPaymentConfirmApi,
  }[props.calculationType];
  try {
    await api(props.calculationForm.id);
  } finally {
    loading.download = false;
  }
};
defineExpose({ init });
</script>

<template>
  <div>
    <BasicCaption content="还本付息试算" />
    <a-descriptions v-bind="descriptionsProp" class="mt-4">
      <a-descriptions-item label="还本付息计划规划方式">
        {{ dictStore.formatter(calculationForm.planningMethod, 'FCT_PLANNING_METHOD') }}
      </a-descriptions-item>
      <a-descriptions-item label="测算综合收益率（%/年）">
        {{ calculationForm.xirrRate }}
      </a-descriptions-item>
      <a-descriptions-item label="拟用信金额（元）" v-if="calculationType === 'CreditPricing'">
        {{ calculationForm.expectedUseAmount }}
      </a-descriptions-item>
      <a-descriptions-item label="计划融资金额（元）" v-if="calculationType === 'Pricing'">
        {{ calculationForm.financingAmount }}
      </a-descriptions-item>
      <a-descriptions-item label="合同利率（%/年）" v-if="calculationType !== 'PaymentRecord'">
        {{ calculationForm.nominalInterestRate }}
      </a-descriptions-item>
      <a-descriptions-item :label="calculationType !== 'PaymentRecord' ? '预计业务投放日' : '起息日'">
        {{ formatDate(calculationForm.expectedLaunchDate) }}
      </a-descriptions-item>
      <a-descriptions-item :label="calculationType !== 'PaymentRecord' ? '预估最后还款日' : '最后还款日'">
        {{ formatDate(calculationForm.expectedDueDate) }}
      </a-descriptions-item>
      <a-descriptions-item label="还本方式">
        {{ dictStore.formatter(calculationForm.principalRepaymentMethod, 'FCT_REPAY_PRINCIPAL_METHOD') }}
      </a-descriptions-item>
      <a-descriptions-item label="还息方式">
        {{ dictStore.formatter(calculationForm.interestRepaymentMethod, 'FCT_REPAY_INTEREST_METHOD') }}
      </a-descriptions-item>
      <template v-if="calculationForm.planningMethod === 'automatic'">
        <a-descriptions-item label="分期还本频次" v-if="calculationForm.principalRepaymentMethod === 'regular'">
          {{ dictStore.formatter(calculationForm.principalPeriod, 'FCT_FREQUENCY') }}
        </a-descriptions-item>
        <a-descriptions-item label="分期还息频次" v-if="calculationForm.interestRepaymentMethod === 'regular'">
          {{ dictStore.formatter(calculationForm.interestPeriod, 'FCT_FREQUENCY') }}
        </a-descriptions-item>

        <a-descriptions-item label="默认当期还本日" v-if="calculationForm.principalRepaymentMethod === 'regular'">
          {{ dictStore.formatter(calculationForm.repayPrincipalDay, 'FCT_CURRENT_REPAY_DATE') }}
        </a-descriptions-item>
        <a-descriptions-item label="默认当期还息日" v-if="calculationForm.interestRepaymentMethod === 'regular'">
          {{ dictStore.formatter(calculationForm.repayInterestDay, 'FCT_CURRENT_REPAY_DATE') }}
        </a-descriptions-item>
      </template>

      <template v-if="calculationForm.planningMethod === 'manual'">
        <a-descriptions-item label="还款期数">
          {{ calculationForm.expectedRepayPeriods }}
        </a-descriptions-item>
      </template>

      <a-descriptions-item label="预估计息天数（天）">
        {{ calculationForm.expectedInterestDays }}
      </a-descriptions-item>

      <a-descriptions-item label="预估还款期数" v-if="calculationForm.planningMethod === 'automatic'">
        {{ calculationForm.expectedRepayPeriods }}
      </a-descriptions-item>
    </a-descriptions>

    <CalculationGrid>
      <template #toolbar-tools>
        <a-space>
          <a-button type="primary" @click="exportCalculation" :loading="loading.download" v-if="calculationType">
            导出
          </a-button>
        </a-space>
      </template>
    </CalculationGrid>
  </div>
</template>

<style></style>
