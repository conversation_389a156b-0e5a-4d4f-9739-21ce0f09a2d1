<script setup lang="ts">
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import { nextTick, ref } from 'vue';

import { DESCRIPTIONS_PROP, DETAIL_GRID_OPTIONS } from '@vben/constants';
import BasicCaption from '@vben/fe-ui/components/Basic/src/BasicCaption.vue';
import { $t } from '@vben/locales';
import { useVbenVxeGrid } from '@vben/plugins/vxe-table';

import { Modal as AModal } from 'ant-design-vue';

import { getCreditPricingCalculationDetailApi, getPricingCalculationDetailApi } from '#/api';
import RepaymentCalculationDetail from '#/views/project/components/repayment-calculation-detail.vue';

const props = defineProps({
  calculationType: { type: String, default: '' },
});
const calculationGridOptions = {
  columns: [
    {
      field: 'financingAmount',
      title: '计划融资金额(元)',
      minWidth: '150px',
    },
    {
      field: 'expectedLaunchDate',
      title: '预估业务投放日',
      minWidth: '150px',
      formatter: 'formatDate',
    },
    {
      field: 'expectedDueDate',
      title: '预估最后还款日',
      minWidth: '150px',
      formatter: 'formatDate',
    },
    {
      field: 'serviceFeeAmount',
      title: '服务费金额(元)',
      minWidth: '150px',
    },
    {
      field: 'nominalInterestRate',
      title: '合同利率(%/年)',
      minWidth: '150px',
    },
    {
      field: 'xirrRate',
      title: '测算综合收益率(%/年)',
      minWidth: '180px',
    },
    {
      field: 'expectedRepayPeriods',
      title: '总还款期数',
      minWidth: '150px',
    },
    {
      field: 'updateTime',
      title: '最后更新时间',
      minWidth: '150px',
      formatter: 'formatDate',
    },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      width: 100,
      slots: { default: 'action' },
    },
  ],
  ...DETAIL_GRID_OPTIONS,
} as VxeTableGridOptions;
const [CalculationGrid, CalculationGridApi] = useVbenVxeGrid({
  gridOptions: calculationGridOptions,
});

const init = (data: any) => {
  CalculationGridApi.grid.reloadData(data.historyCalculationList ?? []);
};

const isViewPopupOpen = ref(false);
const calculationForm = ref({});
const RepaymentCalculationDetailRef = ref();
const handleView = async (row: any = {}) => {
  const api = {
    Pricing: getPricingCalculationDetailApi,
    CreditPricing: getCreditPricingCalculationDetailApi,
  }[props.calculationType];
  calculationForm.value = await api(row.id);
  isViewPopupOpen.value = true;
  nextTick(() => {
    RepaymentCalculationDetailRef.value.init(calculationForm.value);
  });
};

const descriptionsProp = {
  ...DESCRIPTIONS_PROP,
  labelStyle: {
    width: '220px',
    justifyContent: 'flex-end',
  },
};
defineExpose({ init });
</script>

<template>
  <div>
    <BasicCaption content="还本付息试算历史" />
    <CalculationGrid>
      <template #action="{ row }">
        <a-space>
          <a-typography-link @click="handleView(row)">
            {{ $t('base.detail') }}
          </a-typography-link>
        </a-space>
      </template>
    </CalculationGrid>
    <AModal v-model:open="isViewPopupOpen" title="历史版本信息" :footer="null" width="80%">
      <RepaymentCalculationDetail
        ref="RepaymentCalculationDetailRef"
        :key="calculationForm.id"
        :calculation-form="calculationForm"
        :descriptions-prop="descriptionsProp"
      />
    </AModal>
  </div>
</template>
