import type { App } from 'vue';

import { getDownloadFileLinkApi, uploadFileApi } from '#/api/core/file';

// 全局上传配置
export interface UploadConfig {
  uploadApi: Function;
  previewApi: Function;
}

// 默认配置
const defaultConfig: UploadConfig = {
  uploadApi: uploadFileApi,
  previewApi: getDownloadFileLinkApi,
};

// 全局配置存储
let globalUploadConfig: UploadConfig = { ...defaultConfig };

// 设置全局配置
export function setGlobalUploadConfig(config: Partial<UploadConfig>) {
  globalUploadConfig = { ...globalUploadConfig, ...config };
}

// 获取全局配置
export function getGlobalUploadConfig(): UploadConfig {
  return globalUploadConfig;
}

// Vue 插件
export function setupUploadConfig(app: App, config?: Partial<UploadConfig>) {
  if (config) {
    setGlobalUploadConfig(config);
  }

  // 添加全局属性
  app.config.globalProperties.$uploadConfig = globalUploadConfig;

  // 提供注入
  app.provide('uploadConfig', globalUploadConfig);
}

// 组合式函数
export function useUploadConfig() {
  return getGlobalUploadConfig();
}
