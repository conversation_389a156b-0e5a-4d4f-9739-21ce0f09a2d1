<script setup lang="ts">
import type { ConfigInfo } from '@vben/types';

import { provide, ref } from 'vue';

import { Page } from '@vben/common-ui';
import { $t } from '@vben/locales';

import { Card, message, TabPane, Tabs } from 'ant-design-vue';

import { getConfigInfoApi, saveConfigInfoApi } from '#/api';
import AdministratorConfig from '#/views/system/config/components/administrator-config.vue';
// import AiConfig from '#/views/system/config/components/ai-config.vue';
import BaseConfig from '#/views/system/config/components/base-config.vue';
// import CalendarConfig from '#/views/system/config/components/calendar-config.vue';
import SecurityConfig from '#/views/system/config/components/security-config.vue';
// import SyncConfig from '#/views/system/config/components/sync-config.vue';
import {
  configInfoInjectionKey,
  configLoadingInjectionKey,
  configSaveInjection<PERSON>ey,
} from '#/views/system/config/config-injection-key';

const activeKey = ref('base');
const loading = ref({
  detail: false,
  save: false,
});
const configForm = ref<Partial<ConfigInfo>>({});
const getConfigInfo = async () => {
  try {
    loading.value.detail = true;
    configForm.value = await getConfigInfoApi();
  } finally {
    loading.value.detail = false;
  }
};
const saveConfig = async (values: Partial<ConfigInfo>) => {
  try {
    loading.value.save = true;
    await saveConfigInfoApi(values);
    message.success($t('base.resSuccess'));
    await getConfigInfo();
  } finally {
    loading.value.save = false;
  }
};
getConfigInfo();

provide(configInfoInjectionKey, configForm);
provide(configSaveInjectionKey, saveConfig);
provide(configLoadingInjectionKey, loading);
</script>

<template>
  <Page auto-content-height>
    <Card>
      <Tabs v-model:active-key="activeKey">
        <TabPane key="base" :tab="$t('system.config.base')">
          <BaseConfig />
        </TabPane>
        <TabPane key="security" :tab="$t('system.config.security')">
          <SecurityConfig />
        </TabPane>
        <!--<TabPane key="sync" :tab="$t('system.config.sync')">-->
        <!--  <SyncConfig />-->
        <!--</TabPane>-->
        <TabPane key="admin" :tab="$t('system.config.administrator')">
          <AdministratorConfig />
        </TabPane>
        <!--<TabPane key="calendar" :tab="$t('system.config.calendar')">-->
        <!--  <CalendarConfig />-->
        <!--</TabPane>-->
        <!--<TabPane key="ai" :tab="$t('system.config.ai')">-->
        <!--  <AiConfig />-->
        <!--</TabPane>-->
      </Tabs>
    </Card>
  </Page>
</template>

<style></style>
