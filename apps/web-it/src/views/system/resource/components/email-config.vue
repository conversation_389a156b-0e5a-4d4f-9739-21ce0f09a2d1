<script setup lang="ts">
import type { ConfigInfo } from '@vben/types';

import { ref, watch } from 'vue';

import { prompt } from '@vben/common-ui';
import { defineFormOptions } from '@vben/utils';

import { Button, Col, Input, message, Row } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import { debugEmailConfigApi, getConfigInfoApi, saveConfigInfoApi } from '#/api';

const loading = ref({
  detail: false,
  save: false,
});
const configForm = ref<Partial<ConfigInfo>>({});
const getConfigInfo = async () => {
  try {
    loading.value.detail = true;
    configForm.value = await getConfigInfoApi();
  } finally {
    loading.value.detail = false;
  }
};
const saveConfig = async (values: Partial<ConfigInfo>) => {
  try {
    loading.value.save = true;
    await saveConfigInfoApi(values);
    message.success('保存成功');
    await getConfigInfo();
  } finally {
    loading.value.save = false;
  }
};
getConfigInfo();
const [EmailForm, emailFormApi] = useVbenForm(
  defineFormOptions({
    schema: [
      {
        component: 'Input',
        fieldName: 'emailSenderName',
        label: '发件人昵称',
      },
      {
        component: 'Input',
        fieldName: 'emailSmtpHost',
        label: 'SMTP服务器',
        rules: 'required',
      },
      {
        component: 'Input',
        fieldName: 'emailSmtpPort',
        label: 'SMTP端口',
        rules: 'required',
      },
      {
        label: 'SSL安全连接',
        fieldName: 'emailSsl',
        component: 'Switch',
        componentProps: {
          class: '',
        },
      },
      {
        component: 'Input',
        fieldName: 'emailAccount',
        label: 'SMTP用户名',
        rules: 'required',
      },
      {
        component: 'Input',
        fieldName: 'emailPassword',
        label: 'SMTP密码',
        componentProps: {
          type: 'password',
        },
        rules: 'required',
      },
    ],
    wrapperClass: 'grid-cols-1',
    resetButtonOptions: {
      show: false,
    },
    submitButtonOptions: {
      content: '保存',
    },
    handleSubmit(values) {
      saveConfig(values);
    },
  }),
);
watch(
  configForm,
  (val) => {
    emailFormApi.setValues(val, false);
  },
  { immediate: true, deep: true },
);
const testEmailConfig = async () => {
  const res = await emailFormApi.validateAndSubmitForm();
  if (!res) return;
  const value = await prompt({
    content: '请输入收件人邮箱：',
    title: '调试邮箱配置',
    component: Input,
    modelPropName: 'value',
    async beforeClose(scope) {
      if (!scope.isConfirm) return;
      if (!scope.value) {
        message.warning('请输入收件人邮箱');
        return false;
      }
      return true;
    },
  });
  loading.value.save = true;
  try {
    await debugEmailConfigApi({ sendTo: value });
    message.success('调试成功');
  } finally {
    loading.value.save = false;
  }
};
</script>

<template>
  <Row>
    <Col :span="12">
      <div class="m-4">
        <EmailForm :submit-button-options="{ loading: loading.save }">
          <template #reset-before>
            <Button :loading="loading.save" @click="testEmailConfig">调试邮箱配置</Button>
          </template>
        </EmailForm>
      </div>
    </Col>
  </Row>
</template>

<style></style>
