<script setup lang="ts">
import { ref } from 'vue';

import { Page } from '@vben/common-ui';

import { Card, TabPane, Tabs } from 'ant-design-vue';

import EmailConfig from '#/views/system/resource/components/email-config.vue';
// import SmsConfig from '#/views/system/resource/components/sms-config.vue';
import StorageConfig from '#/views/system/resource/components/storage-config.vue';

const activeKey = ref('mail');
</script>

<template>
  <Page auto-content-height>
    <Card>
      <Tabs v-model:active-key="activeKey">
        <TabPane key="mail" tab="邮箱配置">
          <EmailConfig />
        </TabPane>
        <!--<TabPane key="sms" tab="短信配置">-->
        <!--  <SmsConfig />-->
        <!--</TabPane>-->
        <TabPane key="storage" tab="存储配置">
          <StorageConfig />
        </TabPane>
      </Tabs>
    </Card>
  </Page>
</template>

<style scoped></style>
