{"name": "@vben/fe-ui", "version": "0.0.1", "type": "module", "sideEffects": ["**/*.css"], "imports": {"#/*": "./src/*"}, "exports": {".": {"types": "./src/index.ts", "default": "./src/index.ts"}, "./hooks/": {"types": "./src/hooks/", "default": "./src/hooks/"}, "./utils/": {"types": "./src/utils/", "default": "./src/utils/"}, "./components/": {"types": "./src/components/", "default": "./src/components/"}, "./style/": {"types": "./src/style/", "default": "./src/style/"}}, "dependencies": {"@ant-design/icons-vue": "catalog:", "@vben/types": "workspace:*", "@vben/utils": "workspace:*", "@vben/locales": "workspace:*", "@vben-core/shadcn-ui": "workspace:*", "@vueuse/core": "catalog:", "@vue/shared": "catalog:", "ant-design-vue": "catalog:", "dayjs": "catalog:", "vue": "catalog:", "vue-types": "catalog:", "lodash-es": "catalog:", "sortablejs": "catalog:", "resize-observer-polyfill": "catalog:", "@purge-icons/generated": "catalog:"}, "devDependencies": {"@types/lodash-es": "catalog:"}}